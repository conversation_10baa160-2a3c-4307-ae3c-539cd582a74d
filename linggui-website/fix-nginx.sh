#!/bin/bash

# 修复Nginx配置冲突脚本

echo "=== 修复Nginx配置冲突 ==="

# 备份配置文件
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
echo "✅ 已备份nginx.conf"

# 创建新的主配置文件（移除默认server块）
sudo tee /etc/nginx/nginx.conf > /dev/null << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    include /etc/nginx/conf.d/*.conf;
}
EOF

echo "✅ 已更新nginx.conf（移除默认server块）"

# 确保linggui.conf配置正确
sudo tee /etc/nginx/conf.d/linggui.conf > /dev/null << 'EOF'
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    
    server_name _;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

echo "✅ 已更新linggui.conf"

# 测试配置
echo "=== 测试Nginx配置 ==="
if sudo nginx -t; then
    echo "✅ Nginx配置语法正确"
    
    # 重新加载配置
    echo "=== 重新加载Nginx ==="
    sudo systemctl reload nginx
    echo "✅ Nginx已重新加载"
    
    # 等待一下让服务稳定
    sleep 2
    
    # 测试访问
    echo "=== 测试访问 ==="
    echo "测试本地访问:"
    if curl -s -I http://localhost | head -1; then
        echo "✅ 本地访问正常"
    else
        echo "❌ 本地访问失败"
    fi
    
    echo "测试健康检查:"
    if curl -s http://localhost/health; then
        echo "✅ 健康检查正常"
    else
        echo "❌ 健康检查失败"
    fi
    
    echo ""
    echo "🎉 Nginx配置修复完成！"
    echo "现在可以尝试访问: http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn"
    
else
    echo "❌ Nginx配置语法错误，恢复备份"
    sudo cp /etc/nginx/nginx.conf.backup /etc/nginx/nginx.conf
    exit 1
fi
