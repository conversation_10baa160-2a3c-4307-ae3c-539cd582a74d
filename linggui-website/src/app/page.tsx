"use client";

import Image from "next/image";

export default function Home() {
  // 滚动到指定区域
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // 处理表单提交
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('感谢您的咨询！我们的专业顾问将在1小时内与您联系。');
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-indigo-600">灵龟智能</h1>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#home" className="text-gray-900 hover:text-indigo-600 px-3 py-2 text-sm font-medium">首页</a>
              <div className="relative group">
                <button className="text-gray-900 hover:text-indigo-600 px-3 py-2 text-sm font-medium">产品服务</button>
                <div className="absolute top-full left-0 mt-1 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <a href="#communication" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">通信基座</a>
                  <a href="#ai-platform" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">AI平台</a>
                  <a href="#contact-center" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">联络中心</a>
                </div>
              </div>
              <div className="relative group">
                <button className="text-gray-900 hover:text-indigo-600 px-3 py-2 text-sm font-medium">解决方案</button>
                <div className="absolute top-full left-0 mt-1 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <a href="#finance" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">金融行业</a>
                  <a href="#retail" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">零售电商</a>
                  <a href="#travel" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">出行服务</a>
                </div>
              </div>
              <a href="#technology" className="text-gray-900 hover:text-indigo-600 px-3 py-2 text-sm font-medium">技术创新</a>
              <a href="#about" className="text-gray-900 hover:text-indigo-600 px-3 py-2 text-sm font-medium">关于灵龟</a>
              <a href="#contact" className="text-gray-900 hover:text-indigo-600 px-3 py-2 text-sm font-medium">联系我们</a>
            </div>
            <div className="flex items-center">
              <button
                onClick={() => scrollToSection('contact')}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
              >
                产品试用
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="relative py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="mb-6">
              <span className="inline-block bg-indigo-100 text-indigo-800 text-sm px-3 py-1 rounded-full font-medium mb-4">
                全球化通信基座 × AI智能引擎
              </span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              智能联络中心平台
              <span className="text-indigo-600 block">驱动营销服全链路升级</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-4xl mx-auto">
              构建智能、高效、安全的客户联络中心，赋能企业与客户建立更紧密、更智能、更具价值的深度连接，
              驱动企业实现从传统运营模式向智能化运营模式的全面跃迁
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => scrollToSection('contact')}
                className="bg-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors"
              >
                产品试用
              </button>
              <button
                onClick={() => scrollToSection('ai-platform')}
                className="border border-indigo-600 text-indigo-600 px-8 py-3 rounded-lg font-medium hover:bg-indigo-50 transition-colors"
              >
                了解更多
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Core Platform Section */}
      <section id="communication" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">灵龟智能联络中心平台</h2>
            <p className="text-lg text-gray-600">全链路智能交互引擎 重塑客户联络</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-xl">
              <div className="w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">全球通信基座</h3>
              <p className="text-gray-600 mb-4">
                覆盖全国300+城市的号码落地能力，三大运营商深度合作，提供稳定可靠的通信基础设施
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-indigo-600 rounded-full mr-2"></span>
                  支持上万线同时呼叫
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-indigo-600 rounded-full mr-2"></span>
                  千万级平台容量
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-indigo-600 rounded-full mr-2"></span>
                  成本降低90%
                </li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-emerald-100 p-8 rounded-xl">
              <div className="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">全媒体联络中心</h3>
              <p className="text-gray-600 mb-4">
                整合电话、短信、在线、微信、邮件等多种沟通渠道，快速集成客户运营业务系统
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-2"></span>
                  多渠道统一管理
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-2"></span>
                  3分钟快速部署
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-2"></span>
                  隐私保护机制
                </li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-violet-100 p-8 rounded-xl">
              <div className="w-16 h-16 bg-violet-600 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">全场景AI平台</h3>
              <p className="text-gray-600 mb-4">
                基于自研AI中台基座，提供AI外呼、AI客服、AI质检、AI增长等全场景应用
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-violet-600 rounded-full mr-2"></span>
                  实时语音交互
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-violet-600 rounded-full mr-2"></span>
                  智能话术调整
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-violet-600 rounded-full mr-2"></span>
                  多模型适配
                </li>
              </ul>
            </div>
          </div>

          {/* Platform Stats */}
          <div className="bg-gray-50 rounded-2xl p-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-indigo-600 mb-2">300+</div>
                <div className="text-sm text-gray-600">城市号码覆盖</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-indigo-600 mb-2">1万+</div>
                <div className="text-sm text-gray-600">并发呼叫支持</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-indigo-600 mb-2">90%</div>
                <div className="text-sm text-gray-600">成本降低</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-indigo-600 mb-2">300%</div>
                <div className="text-sm text-gray-600">效率提升</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Products Section */}
      <section id="ai-platform" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">全场景AI产品矩阵</h2>
            <p className="text-lg text-gray-600">基于自研AI中台，赋能营销服全场景智能化升级</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {/* AI外呼 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">AI外呼</h3>
              </div>
              <p className="text-gray-600 text-sm mb-3">
                实时语音交互，本地化TTS合成，支持催收、营销、预提醒等多业务场景
              </p>
              <div className="text-xs text-gray-500">
                <span className="inline-block bg-blue-50 text-blue-700 px-2 py-1 rounded mr-2 mb-1">实时交互</span>
                <span className="inline-block bg-blue-50 text-blue-700 px-2 py-1 rounded mr-2 mb-1">智能话术</span>
                <span className="inline-block bg-blue-50 text-blue-700 px-2 py-1 rounded mr-2 mb-1">多场景</span>
              </div>
            </div>

            {/* AI客服 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">AI客服</h3>
              </div>
              <p className="text-gray-600 text-sm mb-3">
                大模型驱动，理解复杂对话，24/7全天候服务，支持大量并发处理
              </p>
              <div className="text-xs text-gray-500">
                <span className="inline-block bg-green-50 text-green-700 px-2 py-1 rounded mr-2 mb-1">大模型</span>
                <span className="inline-block bg-green-50 text-green-700 px-2 py-1 rounded mr-2 mb-1">全天候</span>
                <span className="inline-block bg-green-50 text-green-700 px-2 py-1 rounded mr-2 mb-1">高并发</span>
              </div>
            </div>

            {/* AI质检 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">AI质检</h3>
              </div>
              <p className="text-gray-600 text-sm mb-3">
                高度可配置规则，实时监测，风险预警，大幅降低人工审核成本
              </p>
              <div className="text-xs text-gray-500">
                <span className="inline-block bg-purple-50 text-purple-700 px-2 py-1 rounded mr-2 mb-1">可配置</span>
                <span className="inline-block bg-purple-50 text-purple-700 px-2 py-1 rounded mr-2 mb-1">实时监测</span>
                <span className="inline-block bg-purple-50 text-purple-700 px-2 py-1 rounded mr-2 mb-1">风险预警</span>
              </div>
            </div>

            {/* AI增长 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">AI增长</h3>
              </div>
              <p className="text-gray-600 text-sm mb-3">
                全方位智能互动解决方案，重构行业增长逻辑，驱动企业智能化转型
              </p>
              <div className="text-xs text-gray-500">
                <span className="inline-block bg-orange-50 text-orange-700 px-2 py-1 rounded mr-2 mb-1">增长驱动</span>
                <span className="inline-block bg-orange-50 text-orange-700 px-2 py-1 rounded mr-2 mb-1">智能互动</span>
                <span className="inline-block bg-orange-50 text-orange-700 px-2 py-1 rounded mr-2 mb-1">转型升级</span>
              </div>
            </div>

            {/* AI无人直播 */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">AI无人直播</h3>
              </div>
              <p className="text-gray-600 text-sm mb-3">
                实时互动，数字人主播，适配教育、带货、娱乐等多个场景
              </p>
              <div className="text-xs text-gray-500">
                <span className="inline-block bg-red-50 text-red-700 px-2 py-1 rounded mr-2 mb-1">实时互动</span>
                <span className="inline-block bg-red-50 text-red-700 px-2 py-1 rounded mr-2 mb-1">数字人</span>
                <span className="inline-block bg-red-50 text-red-700 px-2 py-1 rounded mr-2 mb-1">多场景</span>
              </div>
            </div>

            {/* AI Copilot */}
            <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">AI Copilot</h3>
              </div>
              <p className="text-gray-600 text-sm mb-3">
                智能助手，实时辅助人工坐席，提供话术建议和知识支持
              </p>
              <div className="text-xs text-gray-500">
                <span className="inline-block bg-indigo-50 text-indigo-700 px-2 py-1 rounded mr-2 mb-1">智能助手</span>
                <span className="inline-block bg-indigo-50 text-indigo-700 px-2 py-1 rounded mr-2 mb-1">实时辅助</span>
                <span className="inline-block bg-indigo-50 text-indigo-700 px-2 py-1 rounded mr-2 mb-1">知识支持</span>
              </div>
            </div>
          </div>

          {/* AI Platform Architecture */}
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">AI中台架构</h3>
            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="bg-blue-50 p-4 rounded-lg mb-3">
                  <h4 className="font-medium text-gray-900 mb-2">应用层</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>AI外呼</div>
                    <div>AI客服</div>
                    <div>AI质检</div>
                    <div>AI增长</div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <div className="bg-green-50 p-4 rounded-lg mb-3">
                  <h4 className="font-medium text-gray-900 mb-2">渠道层</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>Web端</div>
                    <div>Copilot端</div>
                    <div>企微端</div>
                    <div>Open API</div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <div className="bg-purple-50 p-4 rounded-lg mb-3">
                  <h4 className="font-medium text-gray-900 mb-2">AI中台</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>技能编排</div>
                    <div>知识库</div>
                    <div>机器人管理</div>
                    <div>多模型适配</div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <div className="bg-orange-50 p-4 rounded-lg mb-3">
                  <h4 className="font-medium text-gray-900 mb-2">基础层</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>安全网关</div>
                    <div>权限管理</div>
                    <div>负载均衡</div>
                    <div>监控告警</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">关于灵龟智能</h2>
            <p className="text-lg text-gray-600">汇聚顶尖企业精英，打造智能联络中心领军企业</p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-6">公司愿景</h3>
              <p className="text-lg text-gray-600 mb-6">
                灵龟智能成立于上海，汇聚通信与AI领域资深专家。公司以全球化通信基座为依托，
                致力于打造全渠道客户交互平台及全场景AI引擎，赋能企业构建智能、高效、安全的客户联络中心，
                驱动营销服全链路智能化升级。
              </p>
              <p className="text-lg text-gray-600 mb-6">
                我们致力于用智能交互重构行业增长逻辑，帮助企业与客户建立更紧密、更智能、更具价值的深度连接，
                驱动企业实现从传统运营模式向智能化运营模式的全面跃迁。
              </p>
              <div className="flex flex-wrap gap-3">
                <span className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">通信基座</span>
                <span className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">AI引擎</span>
                <span className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">智能联络</span>
                <span className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">全链路升级</span>
              </div>
            </div>
            <div className="bg-gradient-to-br from-indigo-50 to-blue-100 p-8 rounded-2xl">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">核心优势</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">全球通信资源</h4>
                    <p className="text-gray-600 text-sm">300+城市覆盖，三大运营商深度合作</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">自研AI技术</h4>
                    <p className="text-gray-600 text-sm">大模型驱动，多场景AI应用矩阵</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-white text-sm font-bold">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">企业级安全</h4>
                    <p className="text-gray-600 text-sm">隐私保护，数据安全，合规运营</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Team Section */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-gray-900 mb-8 text-center">精英团队</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white font-bold">阿里</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">阿里巴巴</h4>
                <p className="text-gray-600 text-sm">技术架构与平台建设专家</p>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white font-bold">美团</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">美团</h4>
                <p className="text-gray-600 text-sm">业务增长与运营优化专家</p>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white font-bold">平安</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">平安集团</h4>
                <p className="text-gray-600 text-sm">金融科技与风控专家</p>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-violet-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white font-bold">银行</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">工商银行</h4>
                <p className="text-gray-600 text-sm">企业服务与客户运营专家</p>
              </div>
            </div>
          </div>

          {/* Company Culture */}
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-semibold text-gray-900 mb-8 text-center">团队文化</h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">互信互助</h4>
                <p className="text-gray-600">建立相互信任的工作环境，团队成员互相支持，共同解决挑战</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">专注优势</h4>
                <p className="text-gray-600">每个人专注发挥自己的专业优势，在各自领域追求卓越</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">共同目标</h4>
                <p className="text-gray-600">致力于创造卓越成果，持续追求团队和公司的共同目标</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section id="solutions" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">行业解决方案</h2>
            <p className="text-lg text-gray-600">深耕垂直行业，提供专业化智能联络解决方案</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            {/* 金融行业 */}
            <div id="finance" className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6">
                <div className="flex items-center text-white">
                  <svg className="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-xl font-semibold">金融行业</h3>
                </div>
              </div>
              <div className="p-6">
                <p className="text-gray-600 mb-4">
                  为银行、保险、消费金融等机构提供专业的客户联络解决方案
                </p>
                <div className="space-y-3">
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">银行客户服务与产品营销</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">保险理赔跟进与续保提醒</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">消费金融风控与催收</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 零售电商 */}
            <div id="retail" className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-6">
                <div className="flex items-center text-white">
                  <svg className="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                  <h3 className="text-xl font-semibold">零售电商</h3>
                </div>
              </div>
              <div className="p-6">
                <p className="text-gray-600 mb-4">
                  助力电商平台和品牌零售商提升客户体验和销售转化
                </p>
                <div className="space-y-3">
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">订单确认与物流跟踪</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">会员营销与促销推广</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">AI直播带货与客户回访</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 出行服务 */}
            <div id="travel" className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="bg-gradient-to-r from-purple-500 to-violet-600 p-6">
                <div className="flex items-center text-white">
                  <svg className="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                  <h3 className="text-xl font-semibold">出行服务</h3>
                </div>
              </div>
              <div className="p-6">
                <p className="text-gray-600 mb-4">
                  为航空、汽车、物流等出行服务行业提供智能化客户联络
                </p>
                <div className="space-y-3">
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">航班通知与服务确认</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">汽车销售跟进与保养提醒</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    <span className="text-gray-700">物流配送通知与签收确认</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Business Scenarios */}
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-semibold text-gray-900 mb-8 text-center">业务场景解决方案</h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">营销外呼</h4>
                <p className="text-gray-600 text-sm mb-4">
                  产品推广、客户获取、意向筛选，智能话术实时调整，效果追踪
                </p>
                <div className="flex flex-wrap gap-2 justify-center">
                  <span className="bg-orange-50 text-orange-700 px-2 py-1 rounded text-xs">智能话术</span>
                  <span className="bg-orange-50 text-orange-700 px-2 py-1 rounded text-xs">意向识别</span>
                  <span className="bg-orange-50 text-orange-700 px-2 py-1 rounded text-xs">效果追踪</span>
                </div>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">催收管理</h4>
                <p className="text-gray-600 text-sm mb-4">
                  逾期提醒、还款催收、风险控制，合规话术、情感识别、策略优化
                </p>
                <div className="flex flex-wrap gap-2 justify-center">
                  <span className="bg-red-50 text-red-700 px-2 py-1 rounded text-xs">合规话术</span>
                  <span className="bg-red-50 text-red-700 px-2 py-1 rounded text-xs">情感识别</span>
                  <span className="bg-red-50 text-red-700 px-2 py-1 rounded text-xs">风险控制</span>
                </div>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">客户服务</h4>
                <p className="text-gray-600 text-sm mb-4">
                  咨询解答、问题处理、满意度调研，知识库支持、多轮对话、服务质检
                </p>
                <div className="flex flex-wrap gap-2 justify-center">
                  <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs">知识库</span>
                  <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs">多轮对话</span>
                  <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs">服务质检</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Section */}
      <section id="technology" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">技术创新</h2>
            <p className="text-lg text-gray-600">自研核心技术，构建行业领先的智能联络平台</p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 mb-16">
            {/* AI技术栈 */}
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-6">AI技术栈</h3>
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <svg className="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    大模型技术
                  </h4>
                  <p className="text-gray-600 text-sm">自研AI中台基座，多模型适配能力，知识库管理系统</p>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <svg className="w-6 h-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                    语音技术
                  </h4>
                  <p className="text-gray-600 text-sm">实时语音识别，本地化TTS合成，情感识别分析</p>
                </div>
                <div className="bg-gradient-to-r from-purple-50 to-violet-50 p-6 rounded-xl">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <svg className="w-6 h-6 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    对话技术
                  </h4>
                  <p className="text-gray-600 text-sm">多轮对话管理，意图识别理解，上下文记忆</p>
                </div>
              </div>
            </div>

            {/* 通信技术 */}
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-6">通信技术</h3>
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-xl">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <svg className="w-6 h-6 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    线路资源
                  </h4>
                  <p className="text-gray-600 text-sm">三大运营商合作，全国号码池管理，智能路由分配</p>
                </div>
                <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-6 rounded-xl">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <svg className="w-6 h-6 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    安全技术
                  </h4>
                  <p className="text-gray-600 text-sm">号码隐私保护，数据加密传输，合规监管支持</p>
                </div>
                <div className="bg-gradient-to-r from-teal-50 to-cyan-50 p-6 rounded-xl">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <svg className="w-6 h-6 text-teal-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    平台架构
                  </h4>
                  <p className="text-gray-600 text-sm">高并发处理，高可用架构，弹性扩容能力</p>
                </div>
              </div>
            </div>
          </div>

          {/* Technical Metrics */}
          <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-8">
            <h3 className="text-2xl font-semibold text-gray-900 mb-8 text-center">技术指标</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-indigo-600 mb-2">99.9%</div>
                <div className="text-sm text-gray-600 mb-1">系统可用性</div>
                <div className="text-xs text-gray-500">7×24小时稳定运行</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-indigo-600 mb-2">&lt;100ms</div>
                <div className="text-sm text-gray-600 mb-1">响应延迟</div>
                <div className="text-xs text-gray-500">实时语音交互</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-indigo-600 mb-2">1000万+</div>
                <div className="text-sm text-gray-600 mb-1">平台容量</div>
                <div className="text-xs text-gray-500">支持大规模并发</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-indigo-600 mb-2">97%</div>
                <div className="text-sm text-gray-600 mb-1">语音识别准确率</div>
                <div className="text-xs text-gray-500">行业领先水平</div>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gradient-to-br from-indigo-50 to-blue-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">联系我们</h2>
            <p className="text-lg text-gray-600">期待与您合作，共创智能联络中心新未来</p>
          </div>

          {/* CTA Section */}
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-12">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">立即体验灵龟智能联络中心</h3>
              <p className="text-gray-600 mb-6">
                专业的售前团队为您提供个性化解决方案咨询，助力企业智能化升级
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => {
                    const form = document.querySelector('#contact-form') as HTMLElement;
                    if (form) form.scrollIntoView({ behavior: 'smooth' });
                  }}
                  className="bg-indigo-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors"
                >
                  申请产品试用
                </button>
                <button
                  onClick={() => {
                    window.open('tel:18217575825');
                  }}
                  className="border border-indigo-600 text-indigo-600 px-8 py-3 rounded-lg font-medium hover:bg-indigo-50 transition-colors"
                >
                  预约产品演示
                </button>
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">快速响应</h4>
                <p className="text-gray-600 text-sm">1小时内专业顾问联系</p>
              </div>
              <div>
                <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">专业服务</h4>
                <p className="text-gray-600 text-sm">资深专家一对一咨询</p>
              </div>
              <div>
                <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">定制方案</h4>
                <p className="text-gray-600 text-sm">个性化解决方案设计</p>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">联系信息</h3>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 mb-1">公司地址</p>
                    <p className="text-gray-600">中国（上海）自由贸易试验区临港新片区</p>
                    <p className="text-gray-500 text-sm">宏祥北路83弄1-42号20幢118室</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 mb-1">邮箱地址</p>
                    <a href="mailto:<EMAIL>" className="text-gray-600 hover:text-indigo-600 transition-colors"><EMAIL></a>
                    <p className="text-gray-500 text-sm">商务合作与产品咨询</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 mb-1">联系电话</p>
                    <a href="tel:18217575825" className="text-gray-600 hover:text-indigo-600 transition-colors">18217575825</a>
                    <p className="text-gray-500 text-sm">工作日 9:00-21:00</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">获取专业咨询</h3>
              <form id="contact-form" onSubmit={handleFormSubmit} className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">姓名 *</label>
                    <input type="text" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="请输入您的姓名" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">手机号 *</label>
                    <input type="tel" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="请输入手机号" />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">公司名称</label>
                  <input type="text" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="请输入公司名称" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">行业类型</label>
                  <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    <option value="">请选择行业</option>
                    <option value="finance">金融行业</option>
                    <option value="retail">零售电商</option>
                    <option value="travel">出行服务</option>
                    <option value="education">教育培训</option>
                    <option value="healthcare">医疗健康</option>
                    <option value="other">其他</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">需求描述</label>
                  <textarea rows={4} className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="请简要描述您的需求..."></textarea>
                </div>
                <button type="submit" className="w-full bg-indigo-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
                  提交咨询
                </button>
                <p className="text-xs text-gray-500 text-center">
                  提交后，我们的专业顾问将在1小时内与您联系
                </p>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div className="col-span-2">
              <h3 className="text-2xl font-bold mb-4">灵龟智能</h3>
              <p className="text-gray-300 mb-6 max-w-md">
                以全球化通信基座为依托，打造全渠道客户交互平台及全场景AI引擎，
                赋能企业构建智能、高效、安全的客户联络中心。
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </div>
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors cursor-pointer">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">产品服务</h4>
              <ul className="space-y-3 text-gray-300">
                <li><a href="#communication" className="hover:text-white transition-colors">全球通信基座</a></li>
                <li><a href="#ai-platform" className="hover:text-white transition-colors">AI智能平台</a></li>
                <li><a href="#contact-center" className="hover:text-white transition-colors">联络中心</a></li>
                <li><a href="#" className="hover:text-white transition-colors">技术支持</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">解决方案</h4>
              <ul className="space-y-3 text-gray-300">
                <li><a href="#finance" className="hover:text-white transition-colors">金融行业</a></li>
                <li><a href="#retail" className="hover:text-white transition-colors">零售电商</a></li>
                <li><a href="#travel" className="hover:text-white transition-colors">出行服务</a></li>
                <li><a href="#" className="hover:text-white transition-colors">更多行业</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="grid md:grid-cols-2 gap-4 items-center">
              <div>
                <p className="text-gray-400 text-sm">
                  © 2024 灵龟智能. 保留所有权利. |
                  <a href="#" className="hover:text-white ml-1">隐私政策</a> |
                  <a href="#" className="hover:text-white ml-1">服务条款</a>
                </p>
              </div>
              <div className="md:text-right">
                <p className="text-gray-400 text-sm">
                  沪ICP备xxxxxxxx号 |
                  <span className="ml-1">中国（上海）自由贸易试验区临港新片区</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
