import type { Metadata } from "next";
import { Gei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "灵龟智能 - 全渠道客户交互平台及AI引擎",
  description: "灵龟智能成立于上海，汇聚通信与AI领域资深专家。以全球化通信基座为依托，打造全渠道客户交互平台及全场景AI引擎，赋能企业构建智能、高效、安全的客户联络中心。",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
