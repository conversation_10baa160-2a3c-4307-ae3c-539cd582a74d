# 灵龟智能官网 AWS 部署指南

## 🚀 部署概览

**服务器信息：**
- 实例ID: `i-04f3574ebbd11e89d`
- 公网地址: `ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn`
- 操作系统: Amazon Linux
- 密钥文件: `linggui.pem`

## 📋 部署步骤

### 1. 连接到服务器

```bash
# 设置密钥权限
chmod 400 "linggui.pem"

# 连接到服务器
ssh -i "linggui.pem" <EMAIL>
```

### 2. 安装必要软件

```bash
# 更新系统
sudo yum update -y

# 安装 Node.js 18 (推荐版本)
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version

# 安装 Git
sudo yum install -y git

# 安装 PM2 (进程管理器)
sudo npm install -g pm2
```

### 3. 克隆项目代码

```bash
# 克隆项目
git clone https://gitee.com/leiyuyh/linggui-website.git

# 进入项目目录
cd linggui-website

# 安装依赖
npm install

# 构建生产版本
npm run build
```

### 4. 配置 PM2 启动

```bash
# 创建 PM2 配置文件
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'linggui-website',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/linggui-website',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# 启动应用
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

### 5. 配置 Nginx 反向代理

```bash
# 安装 Nginx
sudo yum install -y nginx

# 创建 Nginx 配置
sudo tee /etc/nginx/conf.d/linggui.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# 启动 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查配置
sudo nginx -t
```

### 6. 配置防火墙

```bash
# 开放 HTTP 端口
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload

# 或者如果使用 iptables
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

## 🔧 常用管理命令

### PM2 管理命令

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs linggui-website

# 重启应用
pm2 restart linggui-website

# 停止应用
pm2 stop linggui-website

# 删除应用
pm2 delete linggui-website
```

### 更新部署

```bash
# 进入项目目录
cd /home/<USER>/linggui-website

# 拉取最新代码
git pull origin main

# 安装新依赖（如果有）
npm install

# 重新构建
npm run build

# 重启应用
pm2 restart linggui-website
```

## 🌐 访问网站

部署完成后，您可以通过以下地址访问网站：
- **HTTP**: http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn

## 🔍 故障排除

### 检查服务状态

```bash
# 检查 PM2 状态
pm2 status

# 检查 Nginx 状态
sudo systemctl status nginx

# 检查端口占用
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :80
```

### 查看日志

```bash
# PM2 应用日志
pm2 logs linggui-website

# Nginx 日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u nginx -f
```

## 📈 性能优化建议

### 1. 启用 Gzip 压缩

在 Nginx 配置中添加：

```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. 配置缓存

```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 监控资源使用

```bash
# 查看内存使用
free -h

# 查看 CPU 使用
top

# 查看磁盘使用
df -h
```

## 🔒 安全建议

1. **定期更新系统**：`sudo yum update -y`
2. **配置防火墙**：只开放必要端口
3. **使用 HTTPS**：配置 SSL 证书
4. **定期备份**：备份代码和配置文件

## 📞 技术支持

如遇到部署问题，请检查：
1. AWS 安全组是否开放了 80 和 443 端口
2. 服务器防火墙配置
3. PM2 和 Nginx 服务状态
4. 应用日志错误信息
