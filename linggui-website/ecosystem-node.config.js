module.exports = {
  apps: [
    {
      name: 'linggui-website',
      script: './node_modules/next/dist/bin/next',
      args: 'start -p 3000 -H 0.0.0.0',
      cwd: '/home/<USER>/linggui-website',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        HOSTNAME: '0.0.0.0'
      },
      // 日志配置
      log_file: '/home/<USER>/logs/linggui-website.log',
      out_file: '/home/<USER>/logs/linggui-website-out.log',
      error_file: '/home/<USER>/logs/linggui-website-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 进程管理配置
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      
      // 启动配置
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // 其他配置
      merge_logs: true,
      time: true
    }
  ]
};
