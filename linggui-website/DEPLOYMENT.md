# 🚀 灵龟智能官网快速部署

## 📋 部署信息

**服务器详情：**
- **实例ID**: `i-04f3574ebbd11e89d`
- **公网地址**: `ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn`
- **系统**: Amazon Linux
- **密钥**: `linggui.pem`

## ⚡ 快速部署（推荐）

### 1. 连接服务器
```bash
chmod 400 "linggui.pem"
ssh -i "linggui.pem" <EMAIL>
```

### 2. 一键部署
```bash
# 下载部署脚本
curl -O https://gitee.com/leiyuyh/linggui-website/raw/main/deploy.sh

# 执行部署
chmod +x deploy.sh
./deploy.sh
```

**就这么简单！** 脚本会自动完成：
- ✅ 安装 Node.js 18
- ✅ 安装 Git 和 PM2
- ✅ 克隆项目代码
- ✅ 构建生产版本
- ✅ 配置 Nginx 反向代理
- ✅ 设置开机自启
- ✅ 配置防火墙

### 3. 访问网站
部署完成后访问：
**http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn**

## 🔄 后续更新

当代码有更新时，只需运行：
```bash
cd /home/<USER>/linggui-website
./update.sh
```

## 🔧 常用管理命令

```bash
# 查看应用状态
pm2 status

# 查看实时日志
pm2 logs linggui-website

# 重启应用
pm2 restart linggui-website

# 查看 Nginx 状态
sudo systemctl status nginx
```

## ⚠️ 重要提醒

### AWS 安全组配置
确保在 AWS 控制台中为实例配置安全组，开放以下端口：
- **HTTP (80)**: 0.0.0.0/0
- **HTTPS (443)**: 0.0.0.0/0  
- **SSH (22)**: 您的IP地址

### 防火墙检查
如果无法访问，检查服务器防火墙：
```bash
# 检查防火墙状态
sudo firewall-cmd --list-all

# 如果需要手动开放端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --reload
```

## 🆘 故障排除

### 网站无法访问
1. 检查 PM2 状态：`pm2 status`
2. 检查 Nginx 状态：`sudo systemctl status nginx`
3. 检查端口：`sudo netstat -tlnp | grep :80`
4. 查看日志：`pm2 logs linggui-website`

### 内存不足
1C1G 配置下的优化建议：
```bash
# 限制 Node.js 内存使用
export NODE_OPTIONS="--max-old-space-size=512"

# 重启应用
pm2 restart linggui-website
```

## 📞 技术支持

如遇问题，请检查：
1. AWS 安全组配置
2. 服务器防火墙设置  
3. PM2 和 Nginx 服务状态
4. 查看详细日志信息

---

**部署完成后，您的灵龟智能官网就可以通过公网访问了！** 🎉
