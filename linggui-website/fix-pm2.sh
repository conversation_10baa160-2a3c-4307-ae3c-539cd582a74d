#!/bin/bash

# 灵龟官网 PM2 部署修复脚本
# 用于解决PM2启动问题

set -e

echo "=== 灵龟官网 PM2 部署修复脚本 ==="
echo "开始时间: $(date)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_DIR="/home/<USER>/linggui-website"
LOG_DIR="/home/<USER>/logs"

# 检查当前目录
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}错误: 项目目录不存在 $PROJECT_DIR${NC}"
    exit 1
fi

cd $PROJECT_DIR

echo -e "${YELLOW}1. 停止现有PM2进程...${NC}"
pm2 stop all || true
pm2 delete all || true

echo -e "${YELLOW}2. 创建日志目录...${NC}"
mkdir -p $LOG_DIR

echo -e "${YELLOW}3. 检查项目构建状态...${NC}"
if [ ! -d ".next" ]; then
    echo -e "${RED}错误: .next目录不存在，需要重新构建${NC}"
    echo "正在构建项目..."
    npm run build --no-lint
fi

echo -e "${YELLOW}4. 检查端口占用...${NC}"
if lsof -i :3000 > /dev/null 2>&1; then
    echo -e "${YELLOW}端口3000被占用，正在释放...${NC}"
    sudo fuser -k 3000/tcp || true
    sleep 2
fi

echo -e "${YELLOW}5. 设置启动脚本权限...${NC}"
chmod +x start-server.js

echo -e "${YELLOW}6. 尝试方案1: 使用标准Next.js启动...${NC}"
pm2 start ecosystem.config.js --env production
sleep 5

# 检查是否成功启动
if pm2 list | grep -q "online"; then
    echo -e "${GREEN}✅ 方案1成功: PM2启动正常${NC}"
    pm2 save
    pm2 startup
    echo -e "${GREEN}PM2已配置为开机自启动${NC}"
else
    echo -e "${RED}❌ 方案1失败，尝试方案2...${NC}"
    pm2 stop all || true
    pm2 delete all || true
    
    echo -e "${YELLOW}7. 尝试方案2: 使用直接node命令...${NC}"
    pm2 start ecosystem-node.config.js --env production
    sleep 5
    
    if pm2 list | grep -q "online"; then
        echo -e "${GREEN}✅ 方案2成功: 直接node命令启动正常${NC}"
        pm2 save
        pm2 startup
    else
        echo -e "${RED}❌ 方案2失败，尝试方案3...${NC}"
        pm2 stop all || true
        pm2 delete all || true
        
        echo -e "${YELLOW}8. 尝试方案3: 使用自定义启动脚本...${NC}"
        pm2 start ecosystem-simple.config.js --env production
        sleep 5
        
        if pm2 list | grep -q "online"; then
            echo -e "${GREEN}✅ 方案3成功: 自定义启动脚本正常${NC}"
            pm2 save
            pm2 startup
        else
            echo -e "${RED}❌ 所有方案都失败了${NC}"
            echo -e "${YELLOW}显示PM2日志进行诊断:${NC}"
            pm2 logs --lines 50
            exit 1
        fi
    fi
fi

echo -e "${YELLOW}9. 验证应用状态...${NC}"
sleep 3
pm2 list
echo ""

echo -e "${YELLOW}10. 测试端口连接...${NC}"
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 应用在3000端口正常响应${NC}"
else
    echo -e "${RED}❌ 应用在3000端口无响应${NC}"
    echo "检查PM2日志:"
    pm2 logs --lines 20
fi

echo -e "${YELLOW}11. 检查Nginx状态...${NC}"
if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✅ Nginx运行正常${NC}"
    
    echo -e "${YELLOW}12. 测试完整访问链路...${NC}"
    if curl -f http://localhost > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 通过Nginx代理访问正常${NC}"
        echo -e "${GREEN}🎉 部署完成! 网站应该可以正常访问了${NC}"
        echo -e "${GREEN}访问地址: http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn${NC}"
    else
        echo -e "${RED}❌ Nginx代理访问失败${NC}"
        echo "检查Nginx配置..."
        sudo nginx -t
    fi
else
    echo -e "${RED}❌ Nginx未运行${NC}"
    echo "启动Nginx..."
    sudo systemctl start nginx
fi

echo ""
echo -e "${GREEN}=== 部署修复完成 ===${NC}"
echo "完成时间: $(date)"
echo ""
echo "常用命令:"
echo "  查看PM2状态: pm2 list"
echo "  查看应用日志: pm2 logs linggui-website"
echo "  重启应用: pm2 restart linggui-website"
echo "  停止应用: pm2 stop linggui-website"
