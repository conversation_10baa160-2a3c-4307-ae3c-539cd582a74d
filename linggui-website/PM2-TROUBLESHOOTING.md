# PM2 部署故障排除指南

## 问题描述
PM2应用状态显示"online"但实际无法连接3000端口，应用在PM2管理下无法正常启动。

## 解决方案

### 方案1: 使用优化的PM2配置（推荐）

```bash
# 1. 停止现有进程
pm2 stop all
pm2 delete all

# 2. 使用新的配置文件启动
pm2 start ecosystem.config.js --env production

# 3. 检查状态
pm2 list
pm2 logs linggui-website
```

### 方案2: 使用直接node命令

如果方案1失败，尝试：

```bash
# 停止现有进程
pm2 stop all
pm2 delete all

# 使用直接node命令配置
pm2 start ecosystem-node.config.js --env production
```

### 方案3: 使用自定义启动脚本

如果前两个方案都失败：

```bash
# 停止现有进程
pm2 stop all
pm2 delete all

# 设置启动脚本权限
chmod +x start-server.js

# 使用自定义启动脚本
pm2 start ecosystem-simple.config.js --env production
```

### 方案4: 手动诊断

如果所有自动方案都失败，手动诊断：

```bash
# 1. 检查项目构建
cd /home/<USER>/linggui-website
ls -la .next/

# 2. 手动测试启动
NODE_ENV=production PORT=3000 npm start

# 3. 检查端口占用
lsof -i :3000
netstat -tlnp | grep :3000

# 4. 检查PM2日志
pm2 logs --lines 50

# 5. 检查系统资源
free -h
df -h
```

## 一键修复脚本

运行自动修复脚本：

```bash
cd /home/<USER>/linggui-website
chmod +x fix-pm2.sh
./fix-pm2.sh
```

## 常见问题

### 1. 内存不足
- 症状：应用启动后立即崩溃
- 解决：调整PM2内存限制，使用 `max_memory_restart: '400M'`

### 2. 端口被占用
- 症状：EADDRINUSE错误
- 解决：`sudo fuser -k 3000/tcp`

### 3. 权限问题
- 症状：无法写入日志文件
- 解决：`mkdir -p /home/<USER>/logs && chmod 755 /home/<USER>/logs`

### 4. Next.js构建问题
- 症状：找不到.next目录
- 解决：重新构建 `npm run build --no-lint`

## 验证部署成功

```bash
# 1. 检查PM2状态
pm2 list

# 2. 检查应用响应
curl http://localhost:3000

# 3. 检查Nginx代理
curl http://localhost

# 4. 检查外部访问
curl http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn
```

## 配置开机自启动

```bash
# 保存当前PM2配置
pm2 save

# 设置开机自启动
pm2 startup

# 按照提示执行sudo命令
```

## 监控和维护

```bash
# 查看实时日志
pm2 logs linggui-website --lines 100

# 重启应用
pm2 restart linggui-website

# 查看应用详细信息
pm2 show linggui-website

# 监控资源使用
pm2 monit
```
