#!/bin/bash

# 更新Nginx配置支持新域名

echo "=== 更新Nginx配置支持域名访问 ==="

# 请替换为你的实际域名
read -p "请输入你的域名（例如：linggui.com）: " DOMAIN_NAME

if [ -z "$DOMAIN_NAME" ]; then
    echo "❌ 域名不能为空"
    exit 1
fi

echo "配置域名: $DOMAIN_NAME"

# 备份当前配置
sudo cp /etc/nginx/conf.d/linggui.conf /etc/nginx/conf.d/linggui.conf.backup

# 更新Nginx配置
sudo tee /etc/nginx/conf.d/linggui.conf > /dev/null << EOF
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    
    server_name $DOMAIN_NAME www.$DOMAIN_NAME _;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
EOF

echo "✅ 已更新Nginx配置"

# 测试配置
if sudo nginx -t; then
    echo "✅ Nginx配置语法正确"
    sudo systemctl reload nginx
    echo "✅ Nginx已重新加载"
    
    echo ""
    echo "🎉 域名配置完成！"
    echo "请确保DNS解析已配置："
    echo "  类型: A记录"
    echo "  主机记录: @"
    echo "  记录值: 52.80.2.213"
    echo "  TTL: 600"
    echo ""
    echo "配置完成后，可通过以下地址访问："
    echo "  http://$DOMAIN_NAME"
    echo "  http://www.$DOMAIN_NAME"
else
    echo "❌ Nginx配置错误，恢复备份"
    sudo cp /etc/nginx/conf.d/linggui.conf.backup /etc/nginx/conf.d/linggui.conf
    exit 1
fi
