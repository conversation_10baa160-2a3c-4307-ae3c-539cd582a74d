#!/bin/bash

# 灵龟智能官网完整部署脚本 (1C1G优化版)
# 适用于直接在服务器上执行

set -e

echo "🚀 开始部署灵龟智能官网 (1C1G优化版)..."

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

# 检查系统
check_system() {
    print_info "检查系统环境..."
    
    # 检查内存
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    print_info "系统内存: ${TOTAL_MEM}MB"
    
    if [ "$TOTAL_MEM" -lt 900 ]; then
        print_warning "内存较低，将进行优化配置"
    fi
    
    # 检查磁盘空间
    DISK_AVAIL=$(df / | awk 'NR==2 {print $4}')
    print_info "可用磁盘空间: ${DISK_AVAIL}KB"
    
    print_success "系统检查完成"
}

# 系统优化
optimize_system() {
    print_info "优化系统配置..."
    
    # 更新系统
    sudo yum update -y
    
    # 停止不必要的服务
    sudo systemctl stop postfix 2>/dev/null || true
    sudo systemctl disable postfix 2>/dev/null || true
    
    # 配置 swap (重要：1G内存需要swap)
    if [ ! -f /swapfile ]; then
        print_info "创建 1GB swap 文件..."
        sudo fallocate -l 1G /swapfile
        sudo chmod 600 /swapfile
        sudo mkswap /swapfile
        sudo swapon /swapfile
        echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
        print_success "Swap 配置完成"
    else
        print_info "Swap 已存在"
    fi
    
    # 优化内存使用
    echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
    echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
    sudo sysctl -p 2>/dev/null || true
    
    print_success "系统优化完成"
}

# 安装 Node.js
install_nodejs() {
    print_info "安装 Node.js 18 LTS..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_info "Node.js 已安装: $NODE_VERSION"
        
        # 检查版本
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 18 ]; then
            print_warning "Node.js 版本过低，需要升级"
            install_node_18
        fi
    else
        install_node_18
    fi
}

install_node_18() {
    # 使用官方二进制包
    curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
    sudo yum install -y nodejs --nogpgcheck
    
    # 配置 npm 使用国内镜像
    npm config set registry https://registry.npmmirror.com
    
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    print_success "Node.js 安装完成: $NODE_VERSION"
    print_success "npm 版本: $NPM_VERSION"
}

# 安装必要软件
install_essentials() {
    print_info "安装必要软件..."
    
    # 安装基础软件
    sudo yum install -y git nginx
    
    # 全局安装 PM2
    sudo npm install -g pm2 --production
    
    print_success "必要软件安装完成"
}

# 克隆项目
setup_project() {
    PROJECT_DIR="$HOME/linggui-website"
    
    if [ -d "$PROJECT_DIR" ]; then
        print_info "更新现有项目..."
        cd "$PROJECT_DIR"
        git pull origin main
    else
        print_info "克隆项目 (浅克隆节省空间)..."
        cd "$HOME"
        git clone --depth 1 https://gitee.com/leiyuyh/linggui-website.git
        cd "$PROJECT_DIR"
    fi
    
    print_success "项目代码准备完成"
}

# 构建项目
build_project() {
    print_info "构建项目 (内存优化模式)..."
    
    # 设置 Node.js 内存限制
    export NODE_OPTIONS="--max-old-space-size=512"
    
    # 只安装生产依赖
    print_info "安装生产依赖..."
    npm ci --only=production
    
    print_info "构建生产版本..."
    npm run build
    
    # 清理构建缓存
    npm cache clean --force
    
    print_success "项目构建完成"
}

# 配置 PM2
setup_pm2() {
    print_info "配置 PM2 (内存优化)..."
    
    # 创建 PM2 配置文件
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'linggui-website',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/linggui-website',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '400M',
    min_uptime: '10s',
    max_restarts: 5,
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      NODE_OPTIONS: '--max-old-space-size=400'
    },
    error_file: '/var/log/linggui-error.log',
    out_file: '/var/log/linggui-out.log',
    log_file: '/var/log/linggui-combined.log',
    time: true
  }]
}
EOF

    # 创建日志目录
    sudo mkdir -p /var/log
    sudo chown ec2-user:ec2-user /var/log/linggui-*.log 2>/dev/null || true
    
    # 停止现有应用
    pm2 delete linggui-website 2>/dev/null || true
    
    # 启动应用
    pm2 start ecosystem.config.js
    
    # 设置开机自启
    pm2 startup systemd -u ec2-user --hp /home/<USER>'^sudo' | bash || true
    pm2 save
    
    print_success "PM2 配置完成"
}

# 配置 Nginx
setup_nginx() {
    print_info "配置 Nginx (轻量版)..."
    
    # 备份原配置
    sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup 2>/dev/null || true
    
    # 创建站点配置
    sudo tee /etc/nginx/conf.d/linggui.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        proxy_pass http://127.0.0.1:3000;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

    # 测试配置
    sudo nginx -t
    
    # 启动 Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    print_success "Nginx 配置完成"
}

# 配置防火墙
setup_firewall() {
    print_info "配置防火墙..."
    
    if systemctl is-active --quiet firewalld; then
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --reload
        print_success "firewalld 配置完成"
    else
        # 使用 iptables
        sudo iptables -I INPUT -p tcp --dport 80 -j ACCEPT 2>/dev/null || true
        sudo iptables -I INPUT -p tcp --dport 443 -j ACCEPT 2>/dev/null || true
        print_success "iptables 配置完成"
    fi
}

# 创建监控脚本
setup_monitoring() {
    print_info "设置监控脚本..."
    
    cat > /home/<USER>/monitor.sh << 'EOF'
#!/bin/bash
echo "=== 灵龟智能官网状态监控 $(date) ==="
echo ""
echo "📊 系统资源:"
echo "内存使用:"
free -h
echo ""
echo "磁盘使用:"
df -h /
echo ""
echo "🚀 应用状态:"
pm2 status
echo ""
echo "🌐 Nginx状态:"
systemctl is-active nginx && echo "✅ Nginx 运行正常" || echo "❌ Nginx 未运行"
echo ""
echo "🔗 端口检查:"
sudo netstat -tlnp | grep :3000 && echo "✅ 应用端口 3000 正常" || echo "❌ 应用端口 3000 异常"
sudo netstat -tlnp | grep :80 && echo "✅ HTTP 端口 80 正常" || echo "❌ HTTP 端口 80 异常"
echo ""
echo "📝 最近日志:"
tail -5 /var/log/linggui-combined.log 2>/dev/null || echo "暂无日志"
echo "========================"
EOF
    
    chmod +x /home/<USER>/monitor.sh
    
    print_success "监控脚本创建完成"
}

# 清理优化
cleanup() {
    print_info "清理临时文件..."
    
    # 清理 yum 缓存
    sudo yum clean all
    
    # 清理 npm 缓存
    npm cache clean --force
    
    # 清理大日志文件
    sudo find /var/log -name "*.log" -type f -size +10M -delete 2>/dev/null || true
    
    print_success "清理完成"
}

# 检查部署状态
check_deployment() {
    print_info "检查部署状态..."
    
    echo ""
    echo "📊 系统资源使用:"
    free -h
    echo ""
    echo "💾 磁盘使用:"
    df -h /
    echo ""
    echo "🚀 PM2 状态:"
    pm2 status
    echo ""
    echo "🌐 Nginx 状态:"
    sudo systemctl status nginx --no-pager -l | head -10
    echo ""
    echo "🔗 端口检查:"
    sudo netstat -tlnp | grep :3000 || echo "端口 3000 未监听"
    sudo netstat -tlnp | grep :80 || echo "端口 80 未监听"
    
    print_success "状态检查完成"
}

# 主函数
main() {
    print_info "开始 1C1G 优化部署流程..."
    
    check_system
    optimize_system
    install_nodejs
    install_essentials
    setup_project
    build_project
    setup_pm2
    setup_nginx
    setup_firewall
    setup_monitoring
    cleanup
    check_deployment
    
    echo ""
    echo "🎉 灵龟智能官网部署完成！"
    echo ""
    echo "📱 访问地址:"
    echo "   http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn"
    echo ""
    echo "🔧 管理命令:"
    echo "   查看状态: pm2 status"
    echo "   系统监控: ./monitor.sh"
    echo "   查看日志: tail -f /var/log/linggui-combined.log"
    echo "   重启应用: pm2 restart linggui-website"
    echo "   重启Nginx: sudo systemctl restart nginx"
    echo ""
    echo "💡 优化特性:"
    echo "   ✅ 1GB Swap 已配置"
    echo "   ✅ Node.js 内存限制 400MB"
    echo "   ✅ Nginx 轻量配置"
    echo "   ✅ 自动重启保护"
    echo "   ✅ 开机自启动"
    echo ""
    echo "🔍 如有问题，请运行: ./monitor.sh"
    echo ""
}

# 执行部署
main "$@"
