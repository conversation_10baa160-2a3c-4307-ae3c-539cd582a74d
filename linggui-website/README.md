# 灵龟智能官网

灵龟智能公司官方网站，展示公司的全渠道客户交互平台及AI引擎业务。

## 🎉 最新更新 (v2.0)

**全新设计风格** - 参照百应科技等行业领先企业的官网设计，打造专业的企业形象：

### ✨ 新增功能
- **完整产品展示**：全球通信基座、全媒体联络中心、全场景AI平台
- **AI产品矩阵**：AI外呼、AI客服、AI质检、AI增长、AI无人直播、AI Copilot
- **行业解决方案**：金融、零售电商、出行服务等垂直行业方案
- **技术创新展示**：AI技术栈、通信技术、核心技术指标
- **专业联系页面**：包含CTA按钮和完整的咨询表单

### 🎨 设计优化
- **现代化UI**：渐变色彩、卡片式布局、专业图标
- **响应式导航**：支持下拉菜单的多级导航
- **信息架构**：清晰的页面层次和内容组织
- **商务风格**：专业的企业级设计语言

## 🚀 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **部署**: 支持 Vercel、Netlify 等平台

## 📋 功能特性

- ✅ 响应式设计，完美适配移动端和桌面端
- ✅ 现代化UI设计，优雅的用户体验
- ✅ SEO友好，完整的元数据配置
- ✅ 快速加载，优化的性能表现
- ✅ 完整的公司展示页面

## 📱 页面结构

- **首页** - 公司介绍和核心价值展示
- **关于我们** - 详细的公司背景和企业文化
- **产品服务** - 三大核心产品展示
- **团队介绍** - 专业团队和团队文化
- **联系我们** - 联系信息和消息发送表单

## 🛠️ 本地开发

### 环境要求

- Node.js 18+
- npm/yarn/pnpm

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看网站。

### 构建生产版本

```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

## 📁 项目结构

```
linggui-website/
├── src/
│   └── app/
│       ├── layout.tsx      # 根布局组件
│       ├── page.tsx        # 首页组件
│       ├── globals.css     # 全局样式
│       └── favicon.ico     # 网站图标
├── public/                 # 静态资源
├── package.json           # 项目配置
├── tailwind.config.ts     # Tailwind配置
├── tsconfig.json          # TypeScript配置
└── next.config.ts         # Next.js配置
```

## 🎨 设计特色

- **现代化设计**: 采用渐变色彩和现代化UI组件
- **专业配色**: 以蓝色为主色调，体现科技感和专业性
- **清晰布局**: 信息层次分明，用户体验友好
- **品牌一致性**: 统一的视觉风格和品牌元素

## 📞 联系信息

- **公司**: 灵龟智能
- **地址**: 上海市浦东新区
- **邮箱**: <EMAIL>
- **电话**: 400-888-8888

## 📄 许可证

© 2024 灵龟智能. 保留所有权利.
