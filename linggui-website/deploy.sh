#!/bin/bash

# 灵龟智能官网自动部署脚本
# 使用方法: ./deploy.sh

set -e  # 遇到错误立即退出

echo "🚀 开始部署灵龟智能官网..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色信息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查是否为 root 用户
check_user() {
    if [ "$EUID" -eq 0 ]; then
        print_error "请不要使用 root 用户运行此脚本"
        exit 1
    fi
}

# 更新系统
update_system() {
    print_info "更新系统包..."
    sudo yum update -y
    print_success "系统更新完成"
}

# 安装 Node.js
install_nodejs() {
    print_info "检查 Node.js 安装状态..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_info "Node.js 已安装，版本: $NODE_VERSION"
        
        # 检查版本是否符合要求 (v18+)
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 18 ]; then
            print_warning "Node.js 版本过低，需要升级到 v18+"
            install_node_18
        fi
    else
        print_info "安装 Node.js 18..."
        install_node_18
    fi
}

install_node_18() {
    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
    sudo yum install -y nodejs
    
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    print_success "Node.js 安装完成: $NODE_VERSION"
    print_success "npm 版本: $NPM_VERSION"
}

# 安装 Git
install_git() {
    if ! command -v git &> /dev/null; then
        print_info "安装 Git..."
        sudo yum install -y git
        print_success "Git 安装完成"
    else
        print_info "Git 已安装"
    fi
}

# 安装 PM2
install_pm2() {
    if ! command -v pm2 &> /dev/null; then
        print_info "安装 PM2..."
        sudo npm install -g pm2
        print_success "PM2 安装完成"
    else
        print_info "PM2 已安装"
    fi
}

# 克隆或更新项目
setup_project() {
    PROJECT_DIR="$HOME/linggui-website"
    
    if [ -d "$PROJECT_DIR" ]; then
        print_info "项目目录已存在，更新代码..."
        cd "$PROJECT_DIR"
        git pull origin main
    else
        print_info "克隆项目代码..."
        cd "$HOME"
        git clone https://gitee.com/leiyuyh/linggui-website.git
        cd "$PROJECT_DIR"
    fi
    
    print_success "项目代码准备完成"
}

# 安装依赖和构建
build_project() {
    print_info "安装项目依赖..."
    npm install
    
    print_info "构建生产版本..."
    npm run build
    
    print_success "项目构建完成"
}

# 配置 PM2
setup_pm2() {
    print_info "配置 PM2..."
    
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'linggui-website',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/linggui-website',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '800M',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
EOF

    # 创建日志目录
    mkdir -p logs
    
    # 停止现有应用（如果存在）
    pm2 delete linggui-website 2>/dev/null || true
    
    # 启动应用
    pm2 start ecosystem.config.js
    
    # 设置开机自启
    pm2 startup | grep -E '^sudo' | bash || true
    pm2 save
    
    print_success "PM2 配置完成"
}

# 安装和配置 Nginx
setup_nginx() {
    print_info "安装和配置 Nginx..."
    
    # 安装 Nginx
    sudo yum install -y nginx
    
    # 创建 Nginx 配置
    sudo tee /etc/nginx/conf.d/linggui.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

    # 测试 Nginx 配置
    sudo nginx -t
    
    # 启动 Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    print_success "Nginx 配置完成"
}

# 配置防火墙
setup_firewall() {
    print_info "配置防火墙..."
    
    # 检查防火墙状态
    if systemctl is-active --quiet firewalld; then
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --reload
        print_success "防火墙配置完成"
    else
        print_warning "firewalld 未运行，请手动检查 iptables 配置"
    fi
}

# 检查部署状态
check_deployment() {
    print_info "检查部署状态..."
    
    # 检查 PM2 状态
    pm2 status
    
    # 检查 Nginx 状态
    sudo systemctl status nginx --no-pager -l
    
    # 检查端口
    print_info "检查端口占用..."
    sudo netstat -tlnp | grep :3000 || print_warning "端口 3000 未监听"
    sudo netstat -tlnp | grep :80 || print_warning "端口 80 未监听"
    
    print_success "部署检查完成"
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🎉 部署完成！"
    echo ""
    echo "📱 访问地址:"
    echo "   HTTP: http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn"
    echo ""
    echo "🔧 管理命令:"
    echo "   查看状态: pm2 status"
    echo "   查看日志: pm2 logs linggui-website"
    echo "   重启应用: pm2 restart linggui-website"
    echo ""
    echo "📋 注意事项:"
    echo "   1. 确保 AWS 安全组开放了 80 和 443 端口"
    echo "   2. 如需域名访问，请配置 DNS 解析"
    echo "   3. 建议配置 SSL 证书启用 HTTPS"
    echo ""
}

# 主函数
main() {
    print_info "开始自动部署流程..."
    
    check_user
    update_system
    install_nodejs
    install_git
    install_pm2
    setup_project
    build_project
    setup_pm2
    setup_nginx
    setup_firewall
    check_deployment
    show_access_info
    
    print_success "🎉 灵龟智能官网部署完成！"
}

# 执行主函数
main "$@"
