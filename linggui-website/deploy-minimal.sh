#!/bin/bash

# 灵龟智能官网轻量级部署脚本 (1C1G优化版)
# 针对低配置服务器优化，去除不必要服务

set -e

echo "🚀 开始轻量级部署 (1C1G优化)..."

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

# 系统优化 - 减少内存使用
optimize_system() {
    print_info "优化系统配置..."
    
    # 停止不必要的服务
    sudo systemctl stop postfix 2>/dev/null || true
    sudo systemctl disable postfix 2>/dev/null || true
    
    # 配置 swap (重要：1G内存需要swap)
    if [ ! -f /swapfile ]; then
        print_info "创建 1GB swap 文件..."
        sudo fallocate -l 1G /swapfile
        sudo chmod 600 /swapfile
        sudo mkswap /swapfile
        sudo swapon /swapfile
        echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
        print_success "Swap 配置完成"
    fi
    
    # 优化内存使用
    echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
    echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
    sudo sysctl -p
    
    print_success "系统优化完成"
}

# 安装 Node.js (轻量版本)
install_nodejs() {
    print_info "安装 Node.js 18 LTS..."
    
    # 使用官方二进制包，避免编译
    curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
    sudo yum install -y nodejs --nogpgcheck
    
    # 配置 npm 使用国内镜像
    npm config set registry https://registry.npmmirror.com
    
    NODE_VERSION=$(node --version)
    print_success "Node.js 安装完成: $NODE_VERSION"
}

# 安装必要软件
install_essentials() {
    print_info "安装必要软件..."
    
    # 只安装必需的包
    sudo yum install -y git nginx
    
    # 全局安装 PM2
    sudo npm install -g pm2 --production
    
    print_success "必要软件安装完成"
}

# 克隆项目 (优化版)
setup_project() {
    PROJECT_DIR="$HOME/linggui-website"
    
    if [ -d "$PROJECT_DIR" ]; then
        print_info "更新现有项目..."
        cd "$PROJECT_DIR"
        git pull origin main
    else
        print_info "克隆项目 (浅克隆节省空间)..."
        cd "$HOME"
        git clone --depth 1 https://gitee.com/leiyuyh/linggui-website.git
        cd "$PROJECT_DIR"
    fi
    
    print_success "项目代码准备完成"
}

# 构建项目 (内存优化)
build_project() {
    print_info "安装依赖 (生产模式)..."
    
    # 设置 Node.js 内存限制
    export NODE_OPTIONS="--max-old-space-size=512"
    
    # 只安装生产依赖
    npm ci --only=production
    
    print_info "构建项目..."
    npm run build
    
    # 清理构建缓存
    npm cache clean --force
    
    print_success "项目构建完成"
}

# 配置 PM2 (轻量版)
setup_pm2() {
    print_info "配置 PM2 (内存优化)..."
    
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'linggui-website',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/linggui-website',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '400M',
    min_uptime: '10s',
    max_restarts: 5,
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      NODE_OPTIONS: '--max-old-space-size=400'
    },
    error_file: '/dev/null',
    out_file: '/dev/null',
    log_file: '/var/log/linggui-app.log',
    time: true
  }]
}
EOF

    # 停止现有应用
    pm2 delete linggui-website 2>/dev/null || true
    
    # 启动应用
    pm2 start ecosystem.config.js
    
    # 设置开机自启 (简化版)
    pm2 startup systemd -u ec2-user --hp /home/<USER>'^sudo' | bash || true
    pm2 save
    
    print_success "PM2 配置完成"
}

# 配置 Nginx (轻量版)
setup_nginx() {
    print_info "配置 Nginx (轻量版)..."
    
    # 备份原配置
    sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
    
    # 创建优化的 Nginx 配置
    sudo tee /etc/nginx/nginx.conf > /dev/null << 'EOF'
user nginx;
worker_processes 1;
worker_rlimit_nofile 1024;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 512;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;
    types_hash_max_size 2048;
    client_max_body_size 1m;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json
        application/xml+rss;
    
    # 虚拟主机配置
    server {
        listen 80;
        server_name ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn;
        
        location / {
            proxy_pass http://127.0.0.1:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
            proxy_pass http://127.0.0.1:3000;
            expires 30d;
            add_header Cache-Control "public, no-transform";
        }
        
        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

    # 测试配置
    sudo nginx -t
    
    # 启动 Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    print_success "Nginx 配置完成"
}

# 配置防火墙 (最小化)
setup_firewall() {
    print_info "配置防火墙..."
    
    if systemctl is-active --quiet firewalld; then
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --reload
    else
        # 使用 iptables
        sudo iptables -I INPUT -p tcp --dport 80 -j ACCEPT
        sudo service iptables save 2>/dev/null || true
    fi
    
    print_success "防火墙配置完成"
}

# 系统监控设置
setup_monitoring() {
    print_info "设置基础监控..."
    
    # 创建简单的监控脚本
    cat > /home/<USER>/monitor.sh << 'EOF'
#!/bin/bash
echo "=== 系统状态 $(date) ==="
echo "内存使用:"
free -h
echo "磁盘使用:"
df -h /
echo "PM2 状态:"
pm2 status
echo "Nginx 状态:"
systemctl is-active nginx
echo "========================"
EOF
    
    chmod +x /home/<USER>/monitor.sh
    
    print_success "监控脚本创建完成"
}

# 清理和优化
cleanup() {
    print_info "清理临时文件..."
    
    # 清理 yum 缓存
    sudo yum clean all
    
    # 清理 npm 缓存
    npm cache clean --force
    
    # 清理日志
    sudo find /var/log -name "*.log" -type f -size +10M -delete 2>/dev/null || true
    
    print_success "清理完成"
}

# 检查部署状态
check_deployment() {
    print_info "检查部署状态..."
    
    echo "内存使用情况:"
    free -h
    
    echo "磁盘使用情况:"
    df -h
    
    echo "PM2 状态:"
    pm2 status
    
    echo "Nginx 状态:"
    sudo systemctl status nginx --no-pager -l
    
    echo "端口检查:"
    sudo netstat -tlnp | grep :3000
    sudo netstat -tlnp | grep :80
    
    print_success "状态检查完成"
}

# 主函数
main() {
    print_info "开始 1C1G 优化部署..."
    
    optimize_system
    install_nodejs
    install_essentials
    setup_project
    build_project
    setup_pm2
    setup_nginx
    setup_firewall
    setup_monitoring
    cleanup
    check_deployment
    
    echo ""
    echo "🎉 轻量级部署完成！"
    echo ""
    echo "📱 访问地址: http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn"
    echo ""
    echo "🔧 管理命令:"
    echo "   查看状态: pm2 status"
    echo "   查看日志: sudo tail -f /var/log/linggui-app.log"
    echo "   系统监控: ./monitor.sh"
    echo "   重启应用: pm2 restart linggui-website"
    echo ""
    echo "💡 优化特性:"
    echo "   - 1GB Swap 已配置"
    echo "   - Node.js 内存限制 400MB"
    echo "   - Nginx 轻量配置"
    echo "   - 自动重启保护"
    echo ""
}

# 执行部署
main "$@"
