#!/bin/bash

# 灵龟智能官网快速更新脚本
# 使用方法: ./update.sh

set -e

echo "🔄 开始更新灵龟智能官网..."

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 进入项目目录
cd /home/<USER>/linggui-website

# 备份当前版本
print_info "备份当前版本..."
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
cp -r . "../$BACKUP_DIR"
print_success "备份完成: ../$BACKUP_DIR"

# 拉取最新代码
print_info "拉取最新代码..."
git pull origin main

# 检查是否有 package.json 变化
if git diff HEAD~1 HEAD --name-only | grep -q "package.json"; then
    print_warning "检测到依赖变化，重新安装..."
    npm install
fi

# 重新构建
print_info "重新构建项目..."
npm run build

# 重启应用
print_info "重启应用..."
pm2 restart linggui-website

# 检查状态
print_info "检查应用状态..."
sleep 3
pm2 status

print_success "🎉 更新完成！"
echo ""
echo "📱 访问地址: http://ec2-52-80-2-213.cn-north-1.compute.amazonaws.com.cn"
echo "📋 如有问题，可恢复备份: cp -r ../$BACKUP_DIR/* ."
