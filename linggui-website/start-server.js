#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// 设置环境变量
process.env.NODE_ENV = 'production';
process.env.PORT = '3000';
process.env.HOSTNAME = '0.0.0.0';

console.log('Starting Linggui Website...');
console.log('Environment:', process.env.NODE_ENV);
console.log('Port:', process.env.PORT);
console.log('Hostname:', process.env.HOSTNAME);

// 启动Next.js应用
const nextPath = path.join(__dirname, 'node_modules', '.bin', 'next');
const child = spawn('node', [nextPath, 'start'], {
  stdio: 'inherit',
  env: process.env,
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});

child.on('exit', (code) => {
  console.log(`Server exited with code ${code}`);
  process.exit(code);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  child.kill('SIGTERM');
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  child.kill('SIGINT');
});
